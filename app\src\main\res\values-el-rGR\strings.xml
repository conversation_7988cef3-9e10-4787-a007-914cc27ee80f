<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Hide My Applist</string>
    <string name="title_home">Αρχική</string>
    <string name="title_logs">Logs</string>
    <string name="title_settings">Ρυθμίσεις</string>
    <string name="title_template_manage">Διαχείριση προτύπου</string>
    <string name="title_template_settings">Ρυθμίσεις προτύπου</string>
    <string name="title_app_manage">Διαχείριση εφαρμογής</string>
    <string name="title_app_select">Επιλογή εφαρμογών</string>
    <string name="title_app_settings">Ρυθμίσεις εφαρμογής</string>
    <string name="title_about">Σχετικ<PERSON></string>
    <string name="do_not_dual">Μην εγκαταστήσεις το HMA στον δευτερεύων χρήστη</string>
    <string name="show_crash_log">Show crash log</string>
    <string name="config_damaged">Το αρχείο ρυθμίσεων είναι κατεστραμμένο. Παρακαλώ καθαρίστε τα δεδομένα της εφαρμογής</string>
    <string name="edit_list">Επεξεργασία λίστας</string>
    <string name="delete">Διαγραφή</string>
    <string name="enabled">Ενεργοποιημένο</string>
    <string name="disabled">Απενεργοποιημένο</string>
    <string name="work_mode">Work mode</string>
    <string name="blacklist">Μαύρη λίστα</string>
    <string name="whitelist">Λευκή λίστα</string>
    <string name="application">Εφαρμογή</string>
    <string name="preferences">Προτιμήσεις</string>
    <string name="refresh">Ανανέωση</string>
    <string name="filter">Φίλτρο</string>
    <string name="yes">Ναι</string>
    <string name="no">Όχι</string>
    <string name="search">Αναζήτηση</string>
    <string name="system">Σύστημα</string>
    <!-- Home -->
    <string name="home_xposed_activated">Module Activated [%s-%d]</string>
    <string name="home_xposed_not_activated">Module Not Activated</string>
    <string name="home_xposed_service_on">Η υπηρεσία συστήματος εκτελείται [%d]</string>
    <string name="home_xposed_service_off">Η υπηρεσία συστήματος δεν εκτελείται</string>
    <string name="home_xposed_service_old">Module updated, need reboot</string>
    <string name="home_xposed_filter_count">Το Hide My Applist έχει φιλτράρει συνολικά %d αιτήματα </string>
    <string name="home_manage">Διαχείριση</string>
    <string name="home_detection_test">Τέστ εντοπισμού</string>
    <string name="home_download_test_app_title">Λήψη της τεστ εφαρμογής</string>
    <string name="home_download_test_app_message">We have developed an individual detection test app and migrated detection test to it. Do you want to download it now?</string>
    <string name="home_backup_and_restore">Backup &amp; Restore</string>
    <string name="home_backup_config">Backup config</string>
    <string name="home_restore_config">Restore config</string>
    <string name="home_import_successful">Επιτυχής εισαγωγή</string>
    <string name="home_import_failed">Ανεπιτυχής εισαγωγή</string>
    <string name="home_import_app_version_too_old">Module version lower than the backup</string>
    <string name="home_import_backup_version_too_old">Backup version too old</string>
    <string name="home_import_file_damaged">Backup file was damaged</string>
    <string name="home_export_failed">Export failed</string>
    <string name="home_exported">Backed up config</string>
    <string name="home_new_update">New update available: %s</string>
    <string name="home_update">Update: %s</string>
    <!-- App select -->
    <string name="list_show_system">Show system apps</string>
    <string name="list_sorting">Sorting</string>
    <string name="list_reverse_order">Reverse order</string>
    <string name="list_sort_by_label">By app name</string>
    <string name="list_sort_by_package_name">By package name</string>
    <string name="list_sort_by_install_time">By install time</string>
    <string name="list_sort_by_update_time">By update time</string>
    <!-- Template manage -->
    <string name="template_usage_hint">Establish templates for \"restless\" apps, controlling applist they can obtain.\n\nBlacklist (recommended): the apps selected in the template will be invisible to the apps applying the template.\nWhitelist: the apps not selected in the template will be invisible to the apps applying the template.</string>
    <string name="template_new_blacklist">Create a blacklist template</string>
    <string name="template_new_whitelist">Create a whitelist template</string>
    <string name="template_delete">Are you sure to delete the template?</string>
    <string name="template_name">Template name</string>
    <string name="template_name_invalid">Name invalid</string>
    <string name="template_delete_title">Delete Template</string>
    <string name="template_name_already_exist">Template name already exist</string>
    <string name="template_applied_count">Applied to %d apps</string>
    <string name="template_apps_visible_count">%d apps visible</string>
    <string name="template_apps_invisible_count">%d apps invisible</string>
    <!-- App settings -->
    <string name="app_enable_hide">Enable hide</string>
    <string name="app_template_config">Template config</string>
    <string name="app_choose_template">Choose template</string>
    <string name="app_exclude_system_apps">Exclude system apps</string>
    <string name="app_exclude_system_apps_summary">Turn off this option only when really necessary. It is likely to cause crash with it disabled</string>
    <string name="app_template_using">Using %d templates</string>
    <string name="app_extra_apps_visible_count">%d additional apps visible</string>
    <string name="app_extra_apps_invisible_count">%d additional apps invisible</string>
    <!-- Logs -->
    <string name="logs_save">Save logs</string>
    <string name="logs_clear">Clear logs</string>
    <string name="logs_empty">No logs to save</string>
    <string name="logs_saved">Logs saved</string>
    <!-- Settings -->
    <string name="settings_module">Module</string>
    <string name="settings_data_isolation">Data isolation</string>
    <string name="settings_data_isolation_summary">App data: %s\nVold app data: %s\nForce mount: %s</string>
    <string name="settings_data_isolation_unsupported">This option is only available on Android 11 and above</string>
    <string name="settings_app_data_isolation">App data isolation</string>
    <string name="settings_vold_app_data_isolation">Vold app data isolation</string>
    <string name="settings_change_require_root">Changing requires root permission</string>
    <string name="settings_need_reboot">Changing requires reboot</string>
    <string name="settings_permission_denied">Permission denied</string>
    <string name="settings_force_mount_data">Enforce data isolation for all apps</string>
    <string name="settings_force_mount_data_summary">Data isolation is disabled for apps target API lower than 30 by default</string>
    <string name="settings_detail_log">Detail log for debugging</string>
    <string name="settings_max_log_size">Logger buffer sizes</string>
    <string name="settings_hide_icon">Hide module icon in launcher</string>
    <string name="settings_hide_icon_summary">You can still open the app in Xposed manager</string>
    <string name="settings_language">Language</string>
    <string name="settings_translate">Participate in translation</string>
    <string name="settings_translate_summary">Help us translate %s into your language</string>
    <string name="settings_theme">Theme</string>
    <string name="settings_system_theme_color">System theme color</string>
    <string name="settings_theme_color">Theme color</string>
    <string name="settings_dark_theme">Dark theme</string>
    <string name="settings_pure_black_dark_theme">Pure black dark theme</string>
    <string name="settings_pure_black_dark_theme_summary">Use the pure black theme if dark theme is enabled</string>
    <string name="settings_service">Service</string>
    <string name="settings_stop_system_service">Stop system service</string>
    <string name="settings_stop_system_service_summary">Need reboot to restart service</string>
    <string name="settings_is_clean_env">Clean runtime environment at the same time?</string>
    <string name="settings_is_clean_env_summary">Remove system service cache in /data/misc/hide_my_applist_*. This means you need to launch the module app once manually after next reboot in order to enable interceptions, and interception counts will also be cleaned.</string>
    <string name="settings_force_clean_env">Force clean runtime environment (root)</string>
    <string name="settings_force_clean_env_summary">Only if system service fails to start, otherwise you should click the above option.</string>
    <string name="settings_force_clean_env_toast_successful">Runtime environment cleaned</string>
    <string name="settings_update">Update</string>
    <string name="settings_disable_update">Disable update notification</string>
    <string name="settings_receive_beta_update">Receive beta update</string>
    <string name="settings_receive_beta_update_summary">Beta version may be unstable</string>
    <!-- About -->
    <string name="about_description">        Although It is incorrect to detect specific app installation, yet not every app using root provides random package name support. In this case, detected apps that use root (such as Fake Location and Storage Isolation) is equal to detected root itself.\n\nAt the same time, some smart apps use various loopholes to acquire your app list, so that it can draw a persona for you.\n\nThis module provides some methods to test whether you have already hidden your applist nicely. Also, it can work as an Xposed module to hide some apps or reject app list requests to protect your privacy.
    </string>
    <string name="about_how_to_use_title">How to use this module</string>
    <string name="about_how_to_use_description_1">        You can create templates in \"Template manage\".\nThen apply the templates to apps in \"App manage\". (You can also select extra apps for targets.)\nYou should and ONLY should check \"System Framework\" in Xposed module scope / whitelist.
    </string>
    <string name="about_how_to_use_description_2">        Config modification effective in real time.
    </string>
    <string name="about_developer">Developer</string>
    <string name="about_support">Support and feedback</string>
    <string name="about_open_source">Open source licenses</string>
    <!-- Colors -->
    <string name="color_sakura">Sakura</string>
    <string name="color_red">Red</string>
    <string name="color_pink">Pink</string>
    <string name="color_purple">Purple</string>
    <string name="color_deep_purple">Deep purple</string>
    <string name="color_indigo">Indigo</string>
    <string name="color_blue">Blue</string>
    <string name="color_light_blue">Light blue</string>
    <string name="color_cyan">Cyan</string>
    <string name="color_teal">Teal</string>
    <string name="color_green">Green</string>
    <string name="color_light_green">Light green</string>
    <string name="color_lime">Lime</string>
    <string name="color_yellow">Yellow</string>
    <string name="color_amber">Amber</string>
    <string name="color_orange">Orange</string>
    <string name="color_deep_orange">Deep orange</string>
    <string name="color_brown">Brown</string>
    <string name="color_blue_grey">Blue grey</string>
</resources>
