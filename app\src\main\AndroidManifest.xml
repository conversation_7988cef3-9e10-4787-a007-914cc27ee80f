<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">

    <uses-permission
        android:name="android.permission.QUERY_ALL_PACKAGES"
        tools:ignore="QueryAllPackagesPermission" />

    <uses-permission android:name="android.permission.INTERNET" />

    <application
        android:name="icu.nullptr.hidemyapplist.MyApp"
        android:allowBackup="true"
        android:fullBackupOnly="true"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:theme="@style/AppTheme">

        <activity
            android:name="icu.nullptr.hidemyapplist.ui.activity.AboutActivity"
            android:theme="@style/AppTheme.About" />
        <activity
            android:name="icu.nullptr.hidemyapplist.ui.activity.MainActivity"
            android:exported="true"
            android:windowSoftInputMode="adjustPan">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="de.robv.android.xposed.category.MODULE_SETTINGS" />
            </intent-filter>
        </activity>

        <activity-alias
            android:name=".MainActivityLauncher"
            android:exported="true"
            android:label="@string/app_name"
            android:targetActivity="icu.nullptr.hidemyapplist.ui.activity.MainActivity">
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />
                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity-alias>

        <provider
            android:name="icu.nullptr.hidemyapplist.service.ServiceProvider"
            android:authorities="com.tsng.hidemyapplist.ServiceProvider"
            android:exported="true"
            tools:ignore="ExportedContentProvider" />

        <meta-data
            android:name="com.google.android.gms.ads.APPLICATION_ID"
            android:value="ca-app-pub-7935522387526005~**********" />
        <meta-data
            android:name="xposedmodule"
            android:value="true" />
        <meta-data
            android:name="xposeddescription"
            android:value="Reject applist detection." />
        <meta-data
            android:name="xposedminversion"
            android:value="82" />
        <meta-data
            android:name="xposedscope"
            android:resource="@array/xposed_scope" />
    </application>
</manifest>
