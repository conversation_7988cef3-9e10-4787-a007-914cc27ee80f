<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            android:elevation="0dp" />
    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <com.google.android.material.card.MaterialCardView
            android:id="@+id/hint_card"
            style="@style/AppTheme.OutlinedCard"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="@dimen/info_card_margin_horizontal"
            android:layout_marginTop="10dp"
            android:transitionName="transition_manage">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="@dimen/item_padding_horizontal"
                android:paddingVertical="@dimen/item_padding_vertical"
                tools:ignore="UseCompoundDrawables">

                <ImageView
                    style="@style/AppTheme.SideImage"
                    android:src="@drawable/outline_info_24"
                    tools:ignore="ContentDescription" />

                <com.google.android.material.textview.MaterialTextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:gravity="center_vertical"
                    android:text="@string/template_usage_hint"
                    android:textAppearance="?textAppearanceBodyMedium" />
            </LinearLayout>
        </com.google.android.material.card.MaterialCardView>

        <icu.nullptr.hidemyapplist.ui.view.ListItemView
            android:id="@+id/new_blacklist_template"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="10dp"
            app:icon="@drawable/baseline_add_24"
            app:text="@string/template_new_blacklist" />

        <icu.nullptr.hidemyapplist.ui.view.ListItemView
            android:id="@+id/new_whitelist_template"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:icon="@drawable/baseline_add_24"
            app:text="@string/template_new_whitelist" />

        <include layout="@layout/line" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/template_list"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_marginTop="10dp"
            android:layout_weight="1" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
