<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:android="http://schemas.android.com/apk/res/android">
    <item
        android:id="@+id/menu_search"
        android:title="@string/search"
        android:actionViewClass="androidx.appcompat.widget.SearchView"
        android:showAsAction="ifRoom" />
    <item
        android:id="@+id/menu_show_system"
        android:checkable="true"
        android:showAsAction="never"
        android:title="@string/list_show_system" />
    <item
        android:showAsAction="never"
        android:title="@string/list_sorting">
        <menu>
            <group android:checkableBehavior="single">
                <item
                    android:id="@+id/menu_sort_by_label"
                    android:title="@string/list_sort_by_label" />
                <item
                    android:id="@+id/menu_sort_by_package_name"
                    android:title="@string/list_sort_by_package_name" />
                <item
                    android:id="@+id/menu_sort_by_install_time"
                    android:title="@string/list_sort_by_install_time" />
                <item
                    android:id="@+id/menu_sort_by_update_time"
                    android:title="@string/list_sort_by_update_time" />
            </group>
            <item
                android:id="@+id/menu_reverse_order"
                android:checkable="true"
                android:title="@string/list_reverse_order" />
        </menu>
    </item>
</menu>
