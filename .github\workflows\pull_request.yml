name: Pull Request

on: pull_request

jobs:
  build:
    name: Build on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        os: [ ubuntu-latest ]
    if: ${{ !startsWith(github.event.head_commit.message, 'docs:') }}

    steps:
      - name: Check out
        uses: actions/checkout@v4
        with:
          submodules: 'recursive'
          fetch-depth: 0

      - name: Gradle wrapper validation
        uses: gradle/wrapper-validation-action@v3

      - name: Set up JDK 21
        uses: actions/setup-java@v4
        with:
          java-version: '21'
          distribution: 'temurin'

      - name: Write properties
        run: |
          echo buildWithGitSuffix=true >> local.properties

      - name: Gradle prebuild
        run: |
          echo 'org.gradle.jvmargs=-Xmx2048m' >> gradle.properties
          ./gradlew prebuild

      - name: Build debug
        id: buildDebug
        run: |
          ./gradlew :app:assembleDebug
          echo "debugName=$(ls app/build/apk/debug/HMA*-debug.apk | awk -F '(/|.apk)' '{print $6}')" >> $GITHUB_OUTPUT
