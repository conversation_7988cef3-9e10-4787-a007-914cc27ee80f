<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="horizontal">

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/level"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:fontFamily="monospace"
        android:gravity="center"
        android:paddingHorizontal="4dp"
        android:textAppearance="?textAppearanceTitleSmall"
        android:textColor="@android:color/white"
        tools:background="@color/info"
        tools:text="I" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:paddingHorizontal="6dp"
        android:paddingVertical="4dp">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/tag"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="monospace"
            android:textAppearance="?textAppearanceTitleSmall"
            tools:text="HMA-Bridge" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginVertical="2dp"
            android:fontFamily="monospace"
            android:textAppearance="?textAppearanceBodySmall"
            tools:text="HMA service initialized" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/date"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="serif"
            android:textAppearance="?textAppearanceBodySmall"
            tools:text="19-19 11:45:14" />
    </LinearLayout>
</LinearLayout>
