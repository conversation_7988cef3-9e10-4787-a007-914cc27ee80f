name: Issue check

on:
  schedule:
  - cron: "0 0 */15 * *"
  issues:
    types: [labeled]

jobs:
  build:

    runs-on: ubuntu-latest
    steps:
    - name: Check title and version requirement
      if: github.event_name == 'issues'
      uses: actions/github-script@v7.0.1
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          let response = await github.rest.issues.listForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
            state: 'open'
          });
          let data = response['data'];
          data.forEach(function(issue){
            var title = issue['title'];
            var body = issue['body'];
            var labels = issue['labels'];
            var spam = false;
            var invalid = false;
            for(var i=0,l=labels.length;i<l;i++){
              if(labels[i]['name'] == 'bug'){
                 if(title.substr(0, 5) !== '[Bug]' || title.trim() == '[Bug]' || title.toLowerCase().includes('hide root')){
                      spam = true;
                  }
                 if(body.search("### Version requirement/版本要求\n\nPublic release/beta version/公开发布/测试版\n\n") != -1 || body.search("### Version requirement/版本要求\n\nOther/其他\n\n") != -1){
                      invalid = true;
                 }
              }
            }
            if(spam){
                var id = issue['number'];
                github.rest.issues.addLabels({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    issue_number: id,
                    labels: ["spam"]
                });
            }
            if(invalid){
                var id = issue['number'];
                github.rest.issues.addLabels({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    issue_number: id,
                    labels: ["invalid"]
                });
            }  
          });
    - name: Close spam and invalid
      if: github.event_name == 'issues'
      uses: actions/github-script@v7.0.1
      with:
        github-token: ${{ secrets.GITHUB_TOKEN }}
        script: |
          let response = await github.rest.issues.listForRepo({
            owner: context.repo.owner,
            repo: context.repo.repo,
            state: 'open'
          });
          let data = response['data'];
          
          data.forEach(function(issue){
            var labels = issue['labels'];
            var close = false;
            var lock = false;
            var rmbug = false;
            for(var i=0,l=labels.length;i<l;i++){
                if(labels[i]['name'] == 'invalid' || labels[i]['name'] == 'spam'){
                    close = true;
                }
                if(labels[i]['name'] == 'spam'){
                    lock = true;
                }
                if(labels[i]['name'] == 'bug'){
                    rmbug = true;
                }
            }
            if(close){
                var id = issue['number'];
                github.rest.issues.createComment({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    issue_number: id,
                    body: 'This issue has been marked as **spam** or **invalid**. For feedback please **follow the rules in the report form**, then open a new issue.'
                });
                if(rmbug){
                  github.rest.issues.removeLabel({
                      owner: context.repo.owner,
                      repo: context.repo.repo,
                      issue_number: id,
                      name: 'bug'
                  });
                }
                github.rest.issues.update({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: id,
                  state: 'closed'
                });
            }
            if(lock){
                var lid = issue['number'];
                github.rest.issues.lock({
                  owner: context.repo.owner,
                  repo: context.repo.repo,
                  issue_number: id,
                  lock_reason: "spam"
                });
            }                
          });
    - name: Close stale
      if: github.event_name != 'issues'
      uses: actions/stale@v9.0.1
      with:
        days-before-stale: 14
        stale-issue-label: stale
        stale-issue-message: It has not been updated for more than 14d. If there is no further update, this Issue will be closed.
        only-labels: bug
        days-before-close: 28
        close-issue-message: It has not been updated for 28 days, so it was automatically closed. Open a new Issue if necessary.
        
