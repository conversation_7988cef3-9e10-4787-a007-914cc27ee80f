<?xml version="1.0" encoding="utf-8"?><!--
  ~ This file is part of LSPosed.
  ~
  ~ LSPosed is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by
  ~ the Free Software Foundation, either version 3 of the License, or
  ~ (at your option) any later version.
  ~
  ~ LSPosed is distributed in the hope that it will be useful,
  ~ but WITHOUT ANY WARRANTY; without even the implied warranty of
  ~ MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  ~ GNU General Public License for more details.
  ~
  ~ You should have received a copy of the GNU General Public License
  ~ along with LSPosed.  If not, see <https://www.gnu.org/licenses/>.
  ~
  ~ Copyright (C) 2021 LSPosed Contributors
  -->

<resources>
    <style name="ThemeOverlay.MaterialRed" parent="ThemeOverlay.Light.MaterialRed"/>
    <style name="ThemeOverlay.MaterialPink" parent="ThemeOverlay.Light.MaterialPink"/>
    <style name="ThemeOverlay.MaterialPurple" parent="ThemeOverlay.Light.MaterialPurple"/>
    <style name="ThemeOverlay.MaterialDeepPurple" parent="ThemeOverlay.Light.MaterialDeepPurple"/>
    <style name="ThemeOverlay.MaterialIndigo" parent="ThemeOverlay.Light.MaterialIndigo"/>
    <style name="ThemeOverlay.MaterialBlue" parent="ThemeOverlay.Light.MaterialBlue"/>
    <style name="ThemeOverlay.MaterialLightBlue" parent="ThemeOverlay.Light.MaterialLightBlue"/>
    <style name="ThemeOverlay.MaterialCyan" parent="ThemeOverlay.Light.MaterialCyan"/>
    <style name="ThemeOverlay.MaterialTeal" parent="ThemeOverlay.Light.MaterialTeal"/>
    <style name="ThemeOverlay.MaterialGreen" parent="ThemeOverlay.Light.MaterialGreen"/>
    <style name="ThemeOverlay.MaterialLightGreen" parent="ThemeOverlay.Light.MaterialLightGreen"/>
    <style name="ThemeOverlay.MaterialLime" parent="ThemeOverlay.Light.MaterialLime"/>
    <style name="ThemeOverlay.MaterialYellow" parent="ThemeOverlay.Light.MaterialYellow"/>
    <style name="ThemeOverlay.MaterialAmber" parent="ThemeOverlay.Light.MaterialAmber"/>
    <style name="ThemeOverlay.MaterialOrange" parent="ThemeOverlay.Light.MaterialOrange"/>
    <style name="ThemeOverlay.MaterialDeepOrange" parent="ThemeOverlay.Light.MaterialDeepOrange"/>
    <style name="ThemeOverlay.MaterialBrown" parent="ThemeOverlay.Light.MaterialBrown"/>
    <style name="ThemeOverlay.MaterialBlueGrey" parent="ThemeOverlay.Light.MaterialBlueGrey"/>
    <style name="ThemeOverlay.MaterialSakura" parent="ThemeOverlay.Light.MaterialSakura"/>
</resources>
