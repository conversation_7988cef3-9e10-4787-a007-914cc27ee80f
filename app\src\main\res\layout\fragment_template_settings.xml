<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="icu.nullptr.hidemyapplist.ui.fragment.TemplateSettingsFragment">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/app_bar"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <com.google.android.material.appbar.MaterialToolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?actionBarSize"
            android:elevation="0dp" />
    </com.google.android.material.appbar.AppBarLayout>

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        app:layout_behavior="@string/appbar_scrolling_view_behavior">

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginVertical="10dp"
            android:hint="@string/template_name"
            android:paddingHorizontal="@dimen/item_padding_horizontal"
            android:transitionName="transition_manage"
            app:startIconDrawable="@drawable/outline_edit_24">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/template_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content" />
        </com.google.android.material.textfield.TextInputLayout>

        <icu.nullptr.hidemyapplist.ui.view.ListItemView
            android:id="@+id/target_apps"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:buttonText="@string/edit_list"
            app:icon="@drawable/baseline_apps_24" />

        <icu.nullptr.hidemyapplist.ui.view.ListItemView
            android:id="@+id/applied_apps"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:buttonText="@string/edit_list"
            app:icon="@drawable/outline_android_24" />
    </LinearLayout>
</androidx.coordinatorlayout.widget.CoordinatorLayout>
