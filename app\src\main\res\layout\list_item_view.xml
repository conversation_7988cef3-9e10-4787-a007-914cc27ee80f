<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?selectableItemBackground"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/item_padding_horizontal"
    tools:context="icu.nullptr.hidemyapplist.ui.view.ListItemView">

    <ImageView
        android:id="@+id/icon"
        style="@style/AppTheme.SideImage"
        android:layout_marginVertical="@dimen/item_padding_vertical"
        tools:ignore="ContentDescription"
        tools:src="@tools:sample/avatars" />

    <com.google.android.material.textview.MaterialTextView
        android:id="@+id/text"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:layout_weight="1"
        android:textAppearance="?textAppearanceBodyLarge"
        tools:text="@tools:sample/full_names" />

    <Button
        android:id="@+id/button"
        style="@style/Widget.Material3.Button.TextButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:visibility="gone"
        tools:text="Button" />
</LinearLayout>
