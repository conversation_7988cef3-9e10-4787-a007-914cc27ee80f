<resources>
    <style name="Base.AppTheme.Light" parent="Theme.Material3.DynamicColors.Light.Rikka">
        <item name="android:windowLightStatusBar">true</item>
        <item name="colorPrimaryVariant">@color/primary_dark_light_light_status_bar</item>

        <item name="android:statusBarColor">?android:colorBackground</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>

        <!-- Preference title -->
        <item name="android:textAppearanceListItem">?textAppearanceSubtitle1</item>
        <!-- Preference category title -->
        <item name="android:colorAccent">?colorPrimary</item>
        <!-- Preference switch -->
        <item name="android:colorControlActivated">?colorPrimary</item>
    </style>

    <style name="Theme.Light" parent="Base.AppTheme.Light" />

    <style name="Base.AppTheme" parent="Theme.Material3.DynamicColors.Dark.Rikka">
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>

        <!-- Preference title -->
        <item name="android:textAppearanceListItem">?textAppearanceSubtitle1</item>
        <!-- Preference category title -->
        <item name="android:colorAccent">?colorPrimary</item>
        <!-- Preference switch -->
        <item name="android:colorControlActivated">?colorPrimary</item>
    </style>

    <style name="Theme" parent="Base.AppTheme" />

    <style name="AppTheme.About" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        <item name="colorPrimary">#154A74</item>
        <item name="colorPrimaryDark">#154A74</item>
    </style>

    <style name="AppTheme.SideImage">
        <item name="android:layout_width">@dimen/vector_icon_size</item>
        <item name="android:layout_height">@dimen/vector_icon_size</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginEnd">16dp</item>
    </style>

    <style name="AppTheme.AppIcon">
        <item name="android:layout_width">@dimen/app_icon_size</item>
        <item name="android:layout_height">@dimen/app_icon_size</item>
        <item name="android:layout_gravity">center_vertical</item>
        <item name="android:layout_marginEnd">16dp</item>
    </style>

    <style name="AppTheme.ElevatedCard" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">30dp</item>
        <item name="cardElevation">@dimen/primary_elevation</item>
    </style>

    <style name="AppTheme.OutlinedCard" parent="Widget.Material3.CardView.Outlined">
        <item name="cardCornerRadius">30dp</item>
        <item name="cardElevation">@dimen/secondary_elevation</item>
    </style>
</resources>
