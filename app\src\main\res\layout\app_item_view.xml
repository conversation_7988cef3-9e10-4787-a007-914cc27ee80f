<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="?selectableItemBackground"
    android:orientation="horizontal"
    android:paddingHorizontal="@dimen/item_padding_horizontal"
    android:paddingVertical="@dimen/item_padding_vertical"
    tools:context="icu.nullptr.hidemyapplist.ui.view.AppItemView">

    <ImageView
        android:id="@+id/icon"
        style="@style/AppTheme.AppIcon"
        tools:ignore="ContentDescription"
        tools:src="@tools:sample/avatars" />

    <LinearLayout
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_weight="1"
        android:orientation="vertical">

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/label"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:textAppearance="?textAppearanceBodyLarge"
            tools:text="@tools:sample/full_names" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/enabled"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="serif"
            android:text="@string/enabled"
            android:textColor="?colorPrimary"
            android:visibility="gone"
            tools:visibility="visible" />

        <com.google.android.material.textview.MaterialTextView
            android:id="@+id/package_name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="monospace"
            tools:text="com.example.app" />
    </LinearLayout>


    <com.google.android.material.checkbox.MaterialCheckBox
        android:id="@+id/checkbox"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center_vertical"
        android:clickable="false" />
</LinearLayout>
