<?xml version="1.0" encoding="utf-8"?>
<PreferenceScreen xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools">
    <PreferenceCategory android:title="@string/application">
        <Preference android:key="appInfo" />
        <SwitchPreference
            android:icon="@drawable/outline_shield_24"
            android:key="enableHide"
            android:title="@string/app_enable_hide" />
    </PreferenceCategory>

    <PreferenceCategory
        android:dependency="enableHide"
        android:title="@string/preferences">
        <SwitchPreference
            android:icon="@drawable/outline_settings_24"
            android:key="useWhiteList"
            android:summaryOff="@string/blacklist"
            android:summaryOn="@string/whitelist"
            android:title="@string/work_mode" />
        <SwitchPreference
            android:dependency="useWhiteList"
            android:disableDependentsState="true"
            android:icon="@drawable/baseline_call_split_24"
            android:key="excludeSystemApps"
            android:summary="@string/app_exclude_system_apps_summary"
            android:title="@string/app_exclude_system_apps" />
    </PreferenceCategory>

    <PreferenceCategory
        android:dependency="enableHide"
        android:title="@string/app_template_config">
        <Preference
            android:icon="@drawable/outline_assignment_24"
            android:key="applyTemplates"
            tools:title="@string/app_template_using" />
        <Preference
            android:icon="@drawable/baseline_apps_24"
            android:key="extraAppList"
            tools:title="@string/app_extra_apps_invisible_count" />
    </PreferenceCategory>
</PreferenceScreen>
