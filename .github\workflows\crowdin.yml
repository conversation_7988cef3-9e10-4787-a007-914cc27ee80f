name: Crowdin Action

on:
  workflow_dispatch:
  push:
    branches: [ master ]
    paths:
      - app/src/main/res/values/strings.xml

jobs:
  synchronize-with-crowdin:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: crowdin action
        uses: crowdin/github-action@master
        with:
          upload_translations: false
          download_translations: false
          upload_sources: true
          config: 'crowdin.yml'
          crowdin_branch_name: master
        env:
          CROWDIN_PROJECT_ID: ${{ secrets.CROWDIN_PROJECT_ID }}
          CROWDIN_API_TOKEN: ${{ secrets.CROWDIN_API_TOKEN }}
