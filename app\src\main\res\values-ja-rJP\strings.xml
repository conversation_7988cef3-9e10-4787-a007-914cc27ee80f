<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Hide My Applist</string>
    <string name="title_home">ホーム</string>
    <string name="title_logs">ログ</string>
    <string name="title_settings">設定</string>
    <string name="title_template_manage">テンプレートの管理</string>
    <string name="title_template_settings">テンプレートの設定</string>
    <string name="title_app_manage">アプリの管理</string>
    <string name="title_app_select">アプリの選択</string>
    <string name="title_app_settings">アプリの設定</string>
    <string name="title_about">About</string>
    <string name="do_not_dual">Do not install HMA to secondary user</string>
    <string name="show_crash_log">クラッシュログの表示</string>
    <string name="config_damaged">構成ファイルが破損しています。 アプリのデータを消去してください。</string>
    <string name="edit_list">リストの編集</string>
    <string name="delete">削除</string>
    <string name="enabled">有効</string>
    <string name="disabled">Disabled</string>
    <string name="work_mode">作業モード</string>
    <string name="blacklist">ブラックリスト</string>
    <string name="whitelist">ホワイトリスト</string>
    <string name="application">アプリケーション</string>
    <string name="preferences">環境設定</string>
    <string name="refresh">更新</string>
    <string name="filter">フィルター</string>
    <string name="yes">はい</string>
    <string name="no">いいえ</string>
    <string name="search">Search</string>
    <string name="system">System</string>
    <!-- Home -->
    <string name="home_xposed_activated">モジュールは有効です [%s-%d]</string>
    <string name="home_xposed_not_activated">モジュールは無効です</string>
    <string name="home_xposed_service_on">システムサービスの実行中 [%d]</string>
    <string name="home_xposed_service_off">システムサービスは実行されていません</string>
    <string name="home_xposed_service_old">モジュールが更新されました。再起動してください。</string>
    <string name="home_xposed_filter_count">Hide My Applist は合計 %d リクエストをフィルタリングしました</string>
    <string name="home_manage">管理</string>
    <string name="home_detection_test">検出のテスト</string>
    <string name="home_download_test_app_title">テストアプリのダウンロード</string>
    <string name="home_download_test_app_message">個別検知テストアプリを開発し、検知テストを移行しました。 今すぐダウンロードしますか？</string>
    <string name="home_backup_and_restore">バックアップ &amp; リストア</string>
    <string name="home_backup_config">コンフィグのバックアップ</string>
    <string name="home_restore_config">コンフィグのリストア</string>
    <string name="home_import_successful">インポート成功</string>
    <string name="home_import_failed">インポートの失敗</string>
    <string name="home_import_app_version_too_old">バックアップよりも低いモジュール バージョン</string>
    <string name="home_import_backup_version_too_old">バックアップのバージョンが古すぎます</string>
    <string name="home_import_file_damaged">バックアップは破損しています</string>
    <string name="home_export_failed">書き出しの失敗</string>
    <string name="home_exported">バックアップ済みのコンフィグ</string>
    <string name="home_new_update">利用可能なアップデート: %s</string>
    <string name="home_update">アップデート: %s</string>
    <!-- App select -->
    <string name="list_show_system">Show system apps</string>
    <string name="list_sorting">並び替え</string>
    <string name="list_reverse_order">逆順</string>
    <string name="list_sort_by_label">アプリ名で</string>
    <string name="list_sort_by_package_name">パッケージネームで</string>
    <string name="list_sort_by_install_time">インストール日時で</string>
    <string name="list_sort_by_update_time">アップデート時間で</string>
    <!-- Template manage -->
    <string name="template_usage_hint">「落ち着きのない」アプリのテンプレートを確立し、取得できるアプリリストを制御します。\n\nブラックリスト (推奨): テンプレートで選択されたアプリは、テンプレートを適用するアプリから見えなくなります。\nホワイトリスト: テンプレートで選択されていないアプリは、テンプレートを適用するアプリには表示されません。</string>
    <string name="template_new_blacklist">ブラックリストのテンプレートを作成</string>
    <string name="template_new_whitelist">ホワイトリストのテンプレートの作成</string>
    <string name="template_delete">テンプレートを削除してもよろしいですか？</string>
    <string name="template_name">テンプレート名</string>
    <string name="template_name_invalid">無効な名前</string>
    <string name="template_delete_title">Delete Template</string>
    <string name="template_name_already_exist">このテンプレート名は既に存在します</string>
    <string name="template_applied_count">%d 個のアプリに適用</string>
    <string name="template_apps_visible_count">%d 個のアプリが表示されます</string>
    <string name="template_apps_invisible_count">%d 個のアプリが表示されません</string>
    <!-- App settings -->
    <string name="app_enable_hide">非表示を有効にする</string>
    <string name="app_template_config">コンフィグのテンプレート</string>
    <string name="app_choose_template">テンプレートのチョイス</string>
    <string name="app_exclude_system_apps">システムアプリを除外</string>
    <string name="app_exclude_system_apps_summary">本当に必要な場合にのみ、このオプションをオフにしてください。 無効にするとクラッシュする可能性があります</string>
    <string name="app_template_using">%d テンプレートの使用</string>
    <string name="app_extra_apps_visible_count">%d 個の追加アプリが表示されます</string>
    <string name="app_extra_apps_invisible_count">%d 個の追加アプリが表示されません</string>
    <!-- Logs -->
    <string name="logs_save">ログの保存</string>
    <string name="logs_clear">ログのクリア</string>
    <string name="logs_empty">保存するログがありません</string>
    <string name="logs_saved">ログを保存しました</string>
    <!-- Settings -->
    <string name="settings_module">モジュール</string>
    <string name="settings_data_isolation">Data isolation</string>
    <string name="settings_data_isolation_summary">App data: %s\nVold app data: %s\nForce mount: %s</string>
    <string name="settings_data_isolation_unsupported">This option is only available on Android 11 and above</string>
    <string name="settings_app_data_isolation">App data isolation</string>
    <string name="settings_vold_app_data_isolation">Vold app data isolation</string>
    <string name="settings_change_require_root">Changing requires root permission</string>
    <string name="settings_need_reboot">Changing requires reboot</string>
    <string name="settings_permission_denied">Permission denied</string>
    <string name="settings_force_mount_data">Enforce data isolation for all apps</string>
    <string name="settings_force_mount_data_summary">Data isolation is disabled for apps target API lower than 30 by default</string>
    <string name="settings_detail_log">デバッグ用詳細ログ</string>
    <string name="settings_max_log_size">ロガーのバッファサイズ</string>
    <string name="settings_hide_icon">ランチャーでモジュール アイコンを非表示にする</string>
    <string name="settings_hide_icon_summary">Xposedマネージャーで引き続きアプリを開くことができます</string>
    <string name="settings_language">言語</string>
    <string name="settings_translate">翻訳に参加する</string>
    <string name="settings_translate_summary">%s をあなたの言語に翻訳するのにご協力ください</string>
    <string name="settings_theme">テーマ</string>
    <string name="settings_system_theme_color">システムのテーマカラー</string>
    <string name="settings_theme_color">テーマカラー</string>
    <string name="settings_dark_theme">ダークテーマ</string>
    <string name="settings_pure_black_dark_theme">ピュアブラックテーマ</string>
    <string name="settings_pure_black_dark_theme_summary">ブラック有効になっている場合は、ピュアブラックのテーマを使用してください</string>
    <string name="settings_service">サービス</string>
    <string name="settings_stop_system_service">システムサービスを停止する</string>
    <string name="settings_stop_system_service_summary">サービスを再開するには再起動が必要です</string>
    <string name="settings_is_clean_env">実行環境のクリーンアップをしますか？</string>
    <string name="settings_is_clean_env_summary">/data/misc/hide_my_applist_* のシステム サービス キャッシュを削除します。つまり、インターセプトを有効にするには、次回の再起動後にモジュール アプリを手動で 1 回起動する必要があり、インターセプト カウントも消去されます。</string>
    <string name="settings_force_clean_env">クリーンなランタイム環境を強制する(root)</string>
    <string name="settings_force_clean_env_summary">システム サービスの開始に失敗した場合のみ、それ以外の場合は上記のオプションをクリックする必要があります。</string>
    <string name="settings_force_clean_env_toast_successful">ランタイムのクリーン</string>
    <string name="settings_update">アップデート</string>
    <string name="settings_disable_update">アップデートの通知を無効にする</string>
    <string name="settings_receive_beta_update">ベータ版アップデートを受け取る</string>
    <string name="settings_receive_beta_update_summary">ベータ版は不安定な場合があります</string>
    <!-- About -->
    <string name="about_description">特定のアプリのインストールを検出するのは正しくありませんが、ルートを使用するすべてのアプリがランダムなパッケージ名のサポートを提供するわけではありません。  この場合、検出された root を使用するアプリ (Fake Location や Storage Isolation など) は、検出された root 自体と同じです。\n\n同時に、一部のスマート アプリは、さまざまな抜け穴を使用してアプリ リストを取得し、描画できるようにします。 \n\nこのモジュールは、アプリリストを適切に非表示にしているかどうかをテストする方法をいくつか提供します。  また、Xposed モジュールとして機能して、一部のアプリを非表示にしたり、アプリ リストのリクエストを拒否してプライバシーを保護したりできます。</string>
    <string name="about_how_to_use_title">このモジュールの使用方法</string>
    <string name="about_how_to_use_description_1">「テンプレート管理」でテンプレートを作成できます。\n次に、「アプリ管理」でテンプレートをアプリに適用します。 (ターゲットに追加のアプリを選択することもできます。)\nXposed モジュール スコープ/ホワイトリストで「システム フレームワーク」をチェックする必要があります。    </string>
    <string name="about_how_to_use_description_2">リアルタイムで有効な構成変更。    </string>
    <string name="about_developer">デベロッパー</string>
    <string name="about_support">サポートとフィードバック</string>
    <string name="about_open_source">オープンソースライセンス</string>
    <!-- Colors -->
    <string name="color_sakura">桜</string>
    <string name="color_red">レッド</string>
    <string name="color_pink">ピンク</string>
    <string name="color_purple">パープル</string>
    <string name="color_deep_purple">ディープパープル</string>
    <string name="color_indigo">インディゴブルー</string>
    <string name="color_blue">ブルー</string>
    <string name="color_light_blue">ライトブルー</string>
    <string name="color_cyan">シアン</string>
    <string name="color_teal">ティール</string>
    <string name="color_green">グリーン</string>
    <string name="color_light_green">ライトグリーン</string>
    <string name="color_lime">ライム</string>
    <string name="color_yellow">イエロー</string>
    <string name="color_amber">アンバー</string>
    <string name="color_orange">オレンジ</string>
    <string name="color_deep_orange">ディープオレンジ</string>
    <string name="color_brown">ブラウン</string>
    <string name="color_blue_grey">グレーブルー</string>
</resources>
