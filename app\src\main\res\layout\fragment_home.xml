<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    tools:context="icu.nullptr.hidemyapplist.ui.fragment.HomeFragment">

    <androidx.coordinatorlayout.widget.CoordinatorLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="10dp"
        app:layout_constraintBottom_toTopOf="@id/ad_banner"
        app:layout_constraintTop_toTopOf="parent">

        <com.google.android.material.appbar.AppBarLayout
            android:id="@+id/app_bar"
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <com.google.android.material.appbar.CollapsingToolbarLayout
                android:id="@+id/toolbar_layout"
                style="?collapsingToolbarLayoutLargeStyle"
                android:layout_width="match_parent"
                android:layout_height="?collapsingToolbarLayoutLargeSize"
                android:fitsSystemWindows="false"
                app:layout_scrollFlags="scroll|exitUntilCollapsed"
                app:titleCollapseMode="scale">

                <com.google.android.material.appbar.MaterialToolbar
                    android:id="@+id/toolbar"
                    android:layout_width="match_parent"
                    android:layout_height="?actionBarSize"
                    android:elevation="0dp"
                    app:layout_collapseMode="pin" />
            </com.google.android.material.appbar.CollapsingToolbarLayout>
        </com.google.android.material.appbar.AppBarLayout>

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="20dp"
            android:scrollbars="none"
            app:layout_behavior="@string/appbar_scrolling_view_behavior">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/status_card"
                    style="@style/AppTheme.ElevatedCard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="10dp"
                    android:clickable="true"
                    android:focusable="true"
                    android:outlineAmbientShadowColor="?colorPrimary"
                    android:outlineSpotShadowColor="?colorPrimary"
                    app:cardBackgroundColor="?colorPrimary">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:paddingHorizontal="@dimen/item_padding_horizontal"
                        android:paddingVertical="@dimen/item_padding_vertical">

                        <ImageView
                            android:id="@+id/module_status_icon"
                            style="@style/AppTheme.SideImage"
                            tools:ignore="ContentDescription"
                            tools:srcCompat="@drawable/outline_done_all_24" />

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="vertical"
                            android:paddingVertical="13dp">

                            <com.google.android.material.textview.MaterialTextView
                                android:id="@+id/module_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:textAppearance="?textAppearanceTitleMedium"
                                android:textColor="?colorOnPrimary"
                                tools:text="@string/home_xposed_activated" />

                            <com.google.android.material.textview.MaterialTextView
                                android:id="@+id/service_status"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="2dp"
                                android:textAppearance="?textAppearanceBodyMedium"
                                android:textColor="?colorOnPrimary"
                                tools:text="@string/home_xposed_service_on" />

                            <com.google.android.material.textview.MaterialTextView
                                android:id="@+id/filter_count"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:layout_marginTop="8dp"
                                android:textAppearance="?textAppearanceBodySmall"
                                android:textColor="?colorOnPrimary"
                                tools:text="@string/home_xposed_filter_count" />
                        </LinearLayout>
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    android:id="@+id/manage_card"
                    style="@style/AppTheme.OutlinedCard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:transitionName="transition_manage">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/item_padding_vertical"
                        android:paddingBottom="5dp">

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal"
                            android:paddingBottom="@dimen/card_title_margin_bottom"
                            android:text="@string/home_manage"
                            android:textAppearance="?textAppearanceTitleMedium" />

                        <icu.nullptr.hidemyapplist.ui.view.ListItemView
                            android:id="@+id/detection_test"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:icon="@drawable/baseline_my_location_24"
                            app:text="@string/home_detection_test" />

                        <include layout="@layout/line" />

                        <icu.nullptr.hidemyapplist.ui.view.ListItemView
                            android:id="@+id/template_manage"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:icon="@drawable/outline_discount_24"
                            app:text="@string/title_template_manage" />

                        <include layout="@layout/line" />

                        <icu.nullptr.hidemyapplist.ui.view.ListItemView
                            android:id="@+id/app_manage"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:icon="@drawable/baseline_apps_24"
                            app:text="@string/title_app_manage" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>

                <com.google.android.material.card.MaterialCardView
                    style="@style/AppTheme.OutlinedCard"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="15dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:paddingTop="@dimen/item_padding_vertical"
                        android:paddingBottom="5dp">

                        <com.google.android.material.textview.MaterialTextView
                            android:layout_width="wrap_content"
                            android:layout_height="match_parent"
                            android:layout_gravity="center_horizontal"
                            android:paddingBottom="@dimen/card_title_margin_bottom"
                            android:text="@string/home_backup_and_restore"
                            android:textAppearance="?textAppearanceTitleMedium" />

                        <icu.nullptr.hidemyapplist.ui.view.ListItemView
                            android:id="@+id/backup_config"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:icon="@drawable/outline_backup_24"
                            app:text="@string/home_backup_config" />

                        <include layout="@layout/line" />

                        <icu.nullptr.hidemyapplist.ui.view.ListItemView
                            android:id="@+id/restore_config"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            app:icon="@drawable/outline_settings_backup_restore_24"
                            app:text="@string/home_restore_config" />
                    </LinearLayout>
                </com.google.android.material.card.MaterialCardView>
            </LinearLayout>
        </androidx.core.widget.NestedScrollView>
    </androidx.coordinatorlayout.widget.CoordinatorLayout>

    <com.google.android.gms.ads.AdView
        android:id="@+id/ad_banner"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="10dp"
        app:adSize="BANNER"
        app:adUnitId="ca-app-pub-7935522387526005/9113700045"
        app:layout_constraintBottom_toBottomOf="parent" />
</androidx.constraintlayout.widget.ConstraintLayout>
