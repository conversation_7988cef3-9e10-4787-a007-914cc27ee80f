<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Hide My Applist</string>
    <string name="title_home">Home</string>
    <string name="title_logs">Logs</string>
    <string name="title_settings">Impostazioni</string>
    <string name="title_template_manage">Gestione template</string>
    <string name="title_template_settings">Impostazioni template</string>
    <string name="title_app_manage">Gestione app</string>
    <string name="title_app_select">Seleziona applicazioni</string>
    <string name="title_app_settings">Impostazioni app</string>
    <string name="title_about">A proposito</string>
    <string name="do_not_dual">Non installare HMA per l\'utente secondario</string>
    <string name="show_crash_log">Mostra crash log</string>
    <string name="config_damaged">File di configurazione danneggiato. Per favore cancella i dati dell\'app</string>
    <string name="edit_list">Modifica lista</string>
    <string name="delete">Elimina</string>
    <string name="enabled">Abilitato</string>
    <string name="disabled">Disabilitato</string>
    <string name="work_mode">Modalità lavoro</string>
    <string name="blacklist">Blacklist</string>
    <string name="whitelist">Whitelist</string>
    <string name="application">Applicazione</string>
    <string name="preferences">Preferenze</string>
    <string name="refresh">Ricarica</string>
    <string name="filter">Filtra</string>
    <string name="yes">Si</string>
    <string name="no">No</string>
    <string name="search">Cerca</string>
    <string name="system">Sistema</string>
    <!-- Home -->
    <string name="home_xposed_activated">Modulo attivato [%s-%d]</string>
    <string name="home_xposed_not_activated">Modulo non attivato</string>
    <string name="home_xposed_service_on">Servizio di sistema in esecuzione [%d]</string>
    <string name="home_xposed_service_off">Servizio di sistema stoppato</string>
    <string name="home_xposed_service_old">Modulo aggiornato, in attesa di riavvio</string>
    <string name="home_xposed_filter_count">Hide My Applist ha filtrato in totale %d richieste</string>
    <string name="home_manage">Gestisci</string>
    <string name="home_detection_test">Test di rilevamento</string>
    <string name="home_download_test_app_title">Scarica app di test</string>
    <string name="home_download_test_app_message">Abbiamo sviluppato un\'app di test di rilevamento individuale e su di essa abbiamo migrato il test di rilevamento. Vuoi scaricarla adesso?</string>
    <string name="home_backup_and_restore">Indietro &amp; Ripristina</string>
    <string name="home_backup_config">Backup configurazione</string>
    <string name="home_restore_config">Ripristina configurazione</string>
    <string name="home_import_successful">Import riuscito</string>
    <string name="home_import_failed">Import fallito</string>
    <string name="home_import_app_version_too_old">Versione del modulo inferiore a quella del backup</string>
    <string name="home_import_backup_version_too_old">Versione backup troppo vecchia</string>
    <string name="home_import_file_damaged">File di backup danneggiato</string>
    <string name="home_export_failed">Export fallito</string>
    <string name="home_exported">Configurazione salvata</string>
    <string name="home_new_update">Nuovo aggiornamento disponibile: %s</string>
    <string name="home_update">Aggiornamento: %s</string>
    <!-- App select -->
    <string name="list_show_system">Mostra app di sistema</string>
    <string name="list_sorting">Ordinamento</string>
    <string name="list_reverse_order">Ordine inverso</string>
    <string name="list_sort_by_label">Per nome app</string>
    <string name="list_sort_by_package_name">Per package name</string>
    <string name="list_sort_by_install_time">Per data installazione</string>
    <string name="list_sort_by_update_time">Per data aggiornamento</string>
    <!-- Template manage -->
    <string name="template_usage_hint">Stabilisci template per app \"nervose\", controllando la lista di app che possono ottenere.\n\nBlacklist (consigliata): le app selezionate nel template saranno invisibili alle app che applicano il template.\nWhitelist: le app non selezionate nel template saranno invisibili alle app che applicano il template.</string>
    <string name="template_new_blacklist">Crea un template blacklist</string>
    <string name="template_new_whitelist">Crea un template whitelist</string>
    <string name="template_delete">Sei sicuro di voler eliminare il template?</string>
    <string name="template_name">Nome template</string>
    <string name="template_name_invalid">Nome non valido</string>
    <string name="template_delete_title">Cancella Template</string>
    <string name="template_name_already_exist">Nome template già esistente</string>
    <string name="template_applied_count">Applicato a %d app</string>
    <string name="template_apps_visible_count">%d app visibili</string>
    <string name="template_apps_invisible_count">%d app invisibili</string>
    <!-- App settings -->
    <string name="app_enable_hide">Abilita nascondimento</string>
    <string name="app_template_config">Configurazione template</string>
    <string name="app_choose_template">Scegli template</string>
    <string name="app_exclude_system_apps">Escludi app di sistema</string>
    <string name="app_exclude_system_apps_summary">Disattiva questa opzione solo quando realmente necessario. È possibile che causi dei crash disabilitandola</string>
    <string name="app_template_using">%d template in uso</string>
    <string name="app_extra_apps_visible_count">%d ulteriori app visibili</string>
    <string name="app_extra_apps_invisible_count">%d ulteriori app invisibili</string>
    <!-- Logs -->
    <string name="logs_save">Salva log</string>
    <string name="logs_clear">Cancella log</string>
    <string name="logs_empty">Nessun log da salvare</string>
    <string name="logs_saved">Log salvati</string>
    <!-- Settings -->
    <string name="settings_module">Modulo</string>
    <string name="settings_data_isolation">Isolamento dati</string>
    <string name="settings_data_isolation_summary">App data: %s\nVold app data: %s\nForce mount: %s</string>
    <string name="settings_data_isolation_unsupported">Questa opzione è disponibile solamente per Android 11 e superiori</string>
    <string name="settings_app_data_isolation">App data isolation</string>
    <string name="settings_vold_app_data_isolation">Vold app data isolation</string>
    <string name="settings_change_require_root">Le modifiche necessitano del permesso di root</string>
    <string name="settings_need_reboot">Changing requires reboot</string>
    <string name="settings_permission_denied">Permission denied</string>
    <string name="settings_force_mount_data">Enforce data isolation for all apps</string>
    <string name="settings_force_mount_data_summary">Data isolation is disabled for apps target API lower than 30 by default</string>
    <string name="settings_detail_log">Log dettagliato per debugging</string>
    <string name="settings_max_log_size">Dimensione buffer log</string>
    <string name="settings_hide_icon">Nascondi icona modulo nel launcher</string>
    <string name="settings_hide_icon_summary">Puoi sempre aprire l\'app in Xposed manager</string>
    <string name="settings_language">Lingua</string>
    <string name="settings_translate">Aiuta a tradurre</string>
    <string name="settings_translate_summary">Aiutaci a tradurre %s nella tua lingua</string>
    <string name="settings_theme">Tema</string>
    <string name="settings_system_theme_color">Colore tema di sistema</string>
    <string name="settings_theme_color">Colore tema</string>
    <string name="settings_dark_theme">Tema scuro</string>
    <string name="settings_pure_black_dark_theme">Tema nero</string>
    <string name="settings_pure_black_dark_theme_summary">Usa tema nero se il tema scuro è abilitato</string>
    <string name="settings_service">Servizio</string>
    <string name="settings_stop_system_service">Stoppa servizio di sistema</string>
    <string name="settings_stop_system_service_summary">È necessario riavviare per riavviare il servizio</string>
    <string name="settings_is_clean_env">Pulire l\'ambiente di runtime allo stesso tempo?</string>
    <string name="settings_is_clean_env_summary">Rimuove la cache del servizio di sistema in /data/misc/hide_my_applist_*. Questo significa che è necessario avviare l\'app del modulo una volta manualmente dopo il prossimo riavvio per abilitare le intercettazioni e anche i conteggi delle intercettazioni verranno resettati.</string>
    <string name="settings_force_clean_env">Forza pulizia ambiente runtime (root)</string>
    <string name="settings_force_clean_env_summary">Solo se il servizio di sistema non riesce ad avviarsi, altrimenti devi cliccare l\'opzione sopra.</string>
    <string name="settings_force_clean_env_toast_successful">Ambiente runtime pulito</string>
    <string name="settings_update">Aggiorna</string>
    <string name="settings_disable_update">Disabilita notifiche di aggiornamento</string>
    <string name="settings_receive_beta_update">Ricevi aggiornamenti beta</string>
    <string name="settings_receive_beta_update_summary">La versione beta potrebbe essere instabile</string>
    <!-- About -->
    <string name="about_description">        Sebbene non sia corretto rilevare l\'installazione di app specifiche, non tutte le app che utilizzano il root forniscono supporto per nome pacchetto casuale. In questo caso, le app rilevate che utilizzano root (come Fake Location e Storage Isolation) sono uguali al rilevamento del root stesso.\n\nAllo stesso tempo, alcune app intelligenti utilizzano varie scappatoie per acquisire il tuo elenco di app, in modo che possano impersonare un profilo per te.\n\nQuesto modulo fornisce alcuni metodi per verificare se hai già nascosto al meglio la tua lista di applicazioni. Inoltre, può funzionare come modulo Xposed per nascondere alcune app o rifiutare le richieste dell\'elenco di app per proteggere la tua privacy.
    </string>
    <string name="about_how_to_use_title">Come usare questo modulo</string>
    <string name="about_how_to_use_description_1">        Puoi creare template in \"Gestione template\".\nPoi applica i template alle app in \"Gestione app\". (Puoi anche selezionare app extra come target.)\nDovresti controllare e SOLO controllare \"System Framework\" in Xposed module scope / whitelist.
    </string>
    <string name="about_how_to_use_description_2">        Modifiche alla configurazione sono effettive in tempo reale.
    </string>
    <string name="about_developer">Sviluppatore</string>
    <string name="about_support">Supporto e feedback</string>
    <string name="about_open_source">Licenze open source</string>
    <!-- Colors -->
    <string name="color_sakura">Sakura</string>
    <string name="color_red">Rosso</string>
    <string name="color_pink">Rosa</string>
    <string name="color_purple">Viola</string>
    <string name="color_deep_purple">Viola scuro</string>
    <string name="color_indigo">Indaco</string>
    <string name="color_blue">Blu</string>
    <string name="color_light_blue">Celeste</string>
    <string name="color_cyan">Ciano</string>
    <string name="color_teal">Verde acqua</string>
    <string name="color_green">Verde</string>
    <string name="color_light_green">Verde chiaro</string>
    <string name="color_lime">Lime</string>
    <string name="color_yellow">Giallo</string>
    <string name="color_amber">Ambra</string>
    <string name="color_orange">Arancione</string>
    <string name="color_deep_orange">Arancione scuro</string>
    <string name="color_brown">Marrone</string>
    <string name="color_blue_grey">Blu grigio</string>
</resources>
