<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">Hide My Applist</string>
    <string name="title_home">Accueil</string>
    <string name="title_logs">Journal</string>
    <string name="title_settings">Réglages</string>
    <string name="title_template_manage">Gestion des modèles</string>
    <string name="title_template_settings">Réglages du modèle</string>
    <string name="title_app_manage">Gestionnaire d\'applications</string>
    <string name="title_app_select">Sélectionner les applications</string>
    <string name="title_app_settings">Réglages pour l\'application</string>
    <string name="title_about">A propos</string>
    <string name="do_not_dual">Ne pas installer HMA dans l\'utilisateur secondaire</string>
    <string name="show_crash_log">Afficher le journal de plantage</string>
    <string name="config_damaged">Fichier de configuration endommagé. <PERSON><PERSON><PERSON> de nettoyer les données de l\'application</string>
    <string name="edit_list">Éditer la liste</string>
    <string name="delete">Supprimer</string>
    <string name="enabled">Activer</string>
    <string name="disabled">Désactivé</string>
    <string name="work_mode">Mode travail</string>
    <string name="blacklist">Liste noir</string>
    <string name="whitelist">Liste blanche</string>
    <string name="application">Application</string>
    <string name="preferences">Réglages</string>
    <string name="refresh">Rafraîchir</string>
    <string name="filter">Filtrer</string>
    <string name="yes">Oui</string>
    <string name="no">Non</string>
    <string name="search">Rechercher</string>
    <string name="system">Système</string>
    <!-- Home -->
    <string name="home_xposed_activated">Module Actif [%s-%d]</string>
    <string name="home_xposed_not_activated">Module Inactivé</string>
    <string name="home_xposed_service_on">Service système en fonctionnement [%d]</string>
    <string name="home_xposed_service_off">Service système ne fonctionne pas</string>
    <string name="home_xposed_service_old">Module mis à jour, redémarrage nécessaire</string>
    <string name="home_xposed_filter_count">L\'application a filtré un total de %d requêtes</string>
    <string name="home_manage">Gérer</string>
    <string name="home_detection_test">Test de détection</string>
    <string name="home_download_test_app_title">Télécharger l\'appli de test</string>
    <string name="home_download_test_app_message">Nous avons développé une appli de test de détection séparée and le test de détection dans celle-ci. Voulez-vous la télécharger maintenant?</string>
    <string name="home_backup_and_restore">Sauvegarder &amp; Restaurer</string>
    <string name="home_backup_config">Configurer la sauvegarde</string>
    <string name="home_restore_config">Restaurer la configuration</string>
    <string name="home_import_successful">Importation réussie</string>
    <string name="home_import_failed">Échec importation</string>
    <string name="home_import_app_version_too_old">Version du module inférieur à la sauvegarde</string>
    <string name="home_import_backup_version_too_old">Version de la sauvegarde trop ancienne</string>
    <string name="home_import_file_damaged">Fichier de sauvegarde est endommagé</string>
    <string name="home_export_failed">Échec de l\'exportation</string>
    <string name="home_exported">Sauvegarder la configuration</string>
    <string name="home_new_update">Nouvelle mise à jour disponible: %s</string>
    <string name="home_update">Mise à jour: %s</string>
    <!-- App select -->
    <string name="list_show_system">Afficher les applications du système</string>
    <string name="list_sorting">Trier</string>
    <string name="list_reverse_order">Inverser l\'ordre</string>
    <string name="list_sort_by_label">Par nom d\'application</string>
    <string name="list_sort_by_package_name">Par nom de paquet</string>
    <string name="list_sort_by_install_time">Par date d\'installation</string>
    <string name="list_sort_by_update_time">Par date de mise à jour</string>
    <!-- Template manage -->
    <string name="template_usage_hint">Établir des modèles pour les applications \"agitées\", controller la liste d\'appli qu\'elles peuvent obtenir.\n\nListe noir (recommandée): les applis sélectionnées dans le modèle seront invisibles aux autres aplis qui appliquent le modèle.\nListe blanche: les applis non sélectionnées dans le modèle seront invisibles aux applis l\'appliquant.</string>
    <string name="template_new_blacklist">Créer un modèle de liste noir</string>
    <string name="template_new_whitelist">Créer un modèle de liste blanche</string>
    <string name="template_delete">Êtes vous sûr de supprimer ce modèle?</string>
    <string name="template_name">Nom du modèle</string>
    <string name="template_name_invalid">Nom invalide</string>
    <string name="template_delete_title">Supprimer Modèle</string>
    <string name="template_name_already_exist">Le nom du modèle éxiste déjà</string>
    <string name="template_applied_count">Appliqué à %d applications</string>
    <string name="template_apps_visible_count">%d applis visibles</string>
    <string name="template_apps_invisible_count">%d applications invisibilisées</string>
    <!-- App settings -->
    <string name="app_enable_hide">Activer le masquage</string>
    <string name="app_template_config">Configuration du modèle</string>
    <string name="app_choose_template">Choisir le modèle</string>
    <string name="app_exclude_system_apps">Exclure les applications du système</string>
    <string name="app_exclude_system_apps_summary">Désactiver cette option uniquement lors ce que c\'est vraiment nécessaire. Il est susceptible de provoquer un crash s\'il est désactivé</string>
    <string name="app_template_using">Utilisation de %d modèle(s)</string>
    <string name="app_extra_apps_visible_count">%d applis additionelles visibles</string>
    <string name="app_extra_apps_invisible_count">%d applis additionelles invisibles</string>
    <!-- Logs -->
    <string name="logs_save">Enregistrer le journal</string>
    <string name="logs_clear">Effacer le journal</string>
    <string name="logs_empty">Aucun journal à enregistrer</string>
    <string name="logs_saved">Journeaux sauvegardés</string>
    <!-- Settings -->
    <string name="settings_module">Module</string>
    <string name="settings_data_isolation">Isolation des données</string>
    <string name="settings_data_isolation_summary">App data: %s\nVold app data: %s\nForce mount: %s</string>
    <string name="settings_data_isolation_unsupported">Cette option est uniquement disponible sur Android 11 et supérieur</string>
    <string name="settings_app_data_isolation">Isolation des données d\'application</string>
    <string name="settings_vold_app_data_isolation">Forcer l\'isolement des données d\'application</string>
    <string name="settings_change_require_root">Changer les permissions d\'accès à la racine requises</string>
    <string name="settings_need_reboot">La modification nécessite un redémarrage</string>
    <string name="settings_permission_denied">Permission refusée</string>
    <string name="settings_force_mount_data">Assurer l\'isolement des données pour toutes les applications</string>
    <string name="settings_force_mount_data_summary">Isolation des données désactivée par défaut pour les applications dont l\'API ciblée est inférieure à 30</string>
    <string name="settings_detail_log">Journeaux détaillés pour débugguage</string>
    <string name="settings_max_log_size">Tailles du tampon d\'enregistrement</string>
    <string name="settings_hide_icon">Masquer l\'icône du module dans le lanceur</string>
    <string name="settings_hide_icon_summary">Vous pouvez toujours ouvrir l\'application depuis le gestionnaire Xposed</string>
    <string name="settings_language">Langue</string>
    <string name="settings_translate">Participer à la traduction</string>
    <string name="settings_translate_summary">Aidez nous à traduire %s dans votre language</string>
    <string name="settings_theme">Thème</string>
    <string name="settings_system_theme_color">Thème de couleur du système</string>
    <string name="settings_theme_color">Couleur du thème</string>
    <string name="settings_dark_theme">Thème sombre</string>
    <string name="settings_pure_black_dark_theme">Thème foncé noir pure</string>
    <string name="settings_pure_black_dark_theme_summary">Utiliser le thème noirci pure si le thème foncé est activé</string>
    <string name="settings_service">Service</string>
    <string name="settings_stop_system_service">Stopper le service du système</string>
    <string name="settings_stop_system_service_summary">Redémarrage nécessaire pour redémarrer le service</string>
    <string name="settings_is_clean_env">Nettoyer l\'environnement d\'éxécution en même temps?</string>
    <string name="settings_is_clean_env_summary">Supprimer le cache du service système dans /data/misc/hide_my_applist_*. Celà veut dire que vous devez lancer le module de l\'application une fois manuellement après le prochain redémarrage pour permettre l\'activation des interceptions, le compteur d\'interceptions sera également nettoyé.</string>
    <string name="settings_force_clean_env">Forcer le nettoyage de l\'environnement (racine)</string>
    <string name="settings_force_clean_env_summary">Uniquement si le service du système échoue à démarrer, autrement vous devriez opter pour l\'option ci-dessus.</string>
    <string name="settings_force_clean_env_toast_successful">Environnement d\'exécution nettoyé</string>
    <string name="settings_update">Mise à jour</string>
    <string name="settings_disable_update">Désactiver la notification de mise à jour</string>
    <string name="settings_receive_beta_update">Recevoir les mises à jours beta</string>
    <string name="settings_receive_beta_update_summary">Les versions Beta pourraient êtres instables</string>
    <!-- About -->
    <string name="about_description">        Bien qu\'il soit incorrect de détecter l\'installation d\'une application spécifique, toutes les applications utilisant la racine ne prennent pas en charge les noms de paquets aléatoires. Dans ce cas, les applications détectées qui utilisent la racine (telles que Fake Location et Storage Isolation) sont égales à la racine détectée elle-même.\n\nDans le même temps, certaines applications intelligentes utilisent diverses failles pour acquérir votre liste d\'applications, afin de vous dessiner un personnage.\n\nCe module fournit quelques méthodes pour tester si vous avez déjà bien caché votre liste d\'applications. De plus, il peut fonctionner comme un module Xposed pour cacher certaines applications ou rejeter les demandes de liste d\'applications afin de protéger votre vie privée.
    </string>
    <string name="about_how_to_use_title">Comment utiliser ce module</string>
    <string name="about_how_to_use_description_1">        Vous pouvez créer des modèles dans \"Gestion des modèles\"\nAppliquer ensuite les modèles aux application dans \"Gestionnaire d\'applications\". (Vous pouvez également sélectionner des applications supplémentaires pour les cibler.)\nVous devez et ne devez QUE vérifier le \"Système d\'injection\" dans la portée du module Xposed / de la liste blanche.
    </string>
    <string name="about_how_to_use_description_2">        Modification de la configuration effective en temps réel.
    </string>
    <string name="about_developer">Développeur</string>
    <string name="about_support">Support et retour d\'expérience</string>
    <string name="about_open_source">Licences open source</string>
    <!-- Colors -->
    <string name="color_sakura">Sakura</string>
    <string name="color_red">Rouge</string>
    <string name="color_pink">Rose</string>
    <string name="color_purple">Violet</string>
    <string name="color_deep_purple">Violet foncé</string>
    <string name="color_indigo">Indigo</string>
    <string name="color_blue">Bleu</string>
    <string name="color_light_blue">Bleu légé</string>
    <string name="color_cyan">Cyan</string>
    <string name="color_teal">Sarcelle</string>
    <string name="color_green">Vert</string>
    <string name="color_light_green">Vert légé</string>
    <string name="color_lime">Citron vert</string>
    <string name="color_yellow">Jaune</string>
    <string name="color_amber">Ambre</string>
    <string name="color_orange">Orange</string>
    <string name="color_deep_orange">Orange foncé</string>
    <string name="color_brown">Marron</string>
    <string name="color_blue_grey">Gris bleuté</string>
</resources>
