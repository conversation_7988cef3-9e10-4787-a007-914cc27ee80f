<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">隱藏應用列表</string>
    <string name="title_home">首頁</string>
    <string name="title_logs">日誌</string>
    <string name="title_settings">設定</string>
    <string name="title_template_manage">模板管理</string>
    <string name="title_template_settings">模板設定</string>
    <string name="title_app_manage">管理應用程式</string>
    <string name="title_app_select">選擇應用程式</string>
    <string name="title_app_settings">應用程式設定</string>
    <string name="title_about">關於</string>
    <string name="do_not_dual">不要多開或將 HMA 安裝給次要使用者</string>
    <string name="show_crash_log">顯示崩潰日誌</string>
    <string name="config_damaged">設定損毀，請清理應用程式的資料</string>
    <string name="edit_list">編輯列表</string>
    <string name="delete">刪除</string>
    <string name="enabled">啟用</string>
    <string name="disabled">停用</string>
    <string name="work_mode">工作模式</string>
    <string name="blacklist">黑名單</string>
    <string name="whitelist">白名單</string>
    <string name="application">應用程式</string>
    <string name="preferences">偏好設定</string>
    <string name="refresh">重新整理</string>
    <string name="filter">篩選</string>
    <string name="yes">是</string>
    <string name="no">否</string>
    <string name="search">搜索</string>
    <string name="system">系統</string>
    <!-- Home -->
    <string name="home_xposed_activated">模組已啟用 [%s-%d]</string>
    <string name="home_xposed_not_activated">模組未啟用</string>
    <string name="home_xposed_service_on">系統服務正在執行 [%d]</string>
    <string name="home_xposed_service_off">系統服務未執行</string>
    <string name="home_xposed_service_old">模組更新，需要重新啟動您的系統</string>
    <string name="home_xposed_filter_count">隱藏應用列表已經執行了 %d 次的攔截</string>
    <string name="home_manage">管理</string>
    <string name="home_detection_test">攔截測試</string>
    <string name="home_download_test_app_title">下載攔截測試應用程式</string>
    <string name="home_download_test_app_message">我們開發了一個獨立的攔截測試應用程式，是否立刻下載使用?</string>
    <string name="home_backup_and_restore">備份 &amp; 還原</string>
    <string name="home_backup_config">備份設定</string>
    <string name="home_restore_config">還原設定</string>
    <string name="home_import_successful">導入成功</string>
    <string name="home_import_failed">導入失敗</string>
    <string name="home_import_app_version_too_old">模組版本低於備份版本</string>
    <string name="home_import_backup_version_too_old">備份版本太舊</string>
    <string name="home_import_file_damaged">備份檔損壞</string>
    <string name="home_export_failed">導出失敗</string>
    <string name="home_exported">備份完成</string>
    <string name="home_new_update">有新的版本更新：%s</string>
    <string name="home_update">更新：%s</string>
    <!-- App select -->
    <string name="list_show_system">顯示系統應用程式</string>
    <string name="list_sorting">排序</string>
    <string name="list_reverse_order">倒序</string>
    <string name="list_sort_by_label">依據應用程式名稱</string>
    <string name="list_sort_by_package_name">依據應用包名稱</string>
    <string name="list_sort_by_install_time">依據安裝時間</string>
    <string name="list_sort_by_update_time">依據更新時間</string>
    <!-- Template manage -->
    <string name="template_usage_hint">為不安分的「應用程式」建立模板，控制他們可讀取的應用程式列表。\n黑名單 (推薦使用)：開啟此模板的應用程式將看不到黑名單內勾選的應用程式。\n白名單：開啟此模板的應用程式將只能看到白名單模板勾選內的應用程式。</string>
    <string name="template_new_blacklist">建立黑名單模板</string>
    <string name="template_new_whitelist">建立白名單模板</string>
    <string name="template_delete">你確定要刪除該模板嗎?</string>
    <string name="template_name">模板名稱</string>
    <string name="template_name_invalid">名稱無效</string>
    <string name="template_delete_title">删除模板</string>
    <string name="template_name_already_exist">模板名稱已經存在</string>
    <string name="template_applied_count">已對 %d 個應用程式啟用</string>
    <string name="template_apps_visible_count">對 %d 個應用程式顯示</string>
    <string name="template_apps_invisible_count">對 %d 個應用程式執行攔截</string>
    <!-- App settings -->
    <string name="app_enable_hide">啟用隱藏</string>
    <string name="app_template_config">模板設定</string>
    <string name="app_choose_template">選擇模板</string>
    <string name="app_exclude_system_apps">排除系統應用程式</string>
    <string name="app_exclude_system_apps_summary">只有在真正必要時才關閉這個選項。如果關閉它，很可能會導致系統或應用崩潰</string>
    <string name="app_template_using">已使用 %d 個模板</string>
    <string name="app_extra_apps_visible_count">額外新增 %d 個可見的應用程式</string>
    <string name="app_extra_apps_invisible_count">額外新增 %d 個攔截的應用程式</string>
    <!-- Logs -->
    <string name="logs_save">另存日誌</string>
    <string name="logs_clear">清除日誌</string>
    <string name="logs_empty">沒有日誌可另存</string>
    <string name="logs_saved">日誌已儲存</string>
    <!-- Settings -->
    <string name="settings_module">模塊</string>
    <string name="settings_data_isolation">資料隔離</string>
    <string name="settings_data_isolation_summary">App data：%s\nVold app data：%s\n強制掛載：%s</string>
    <string name="settings_data_isolation_unsupported">該選項僅適用於 Android 11 或更低版本的系統</string>
    <string name="settings_app_data_isolation">App data 隔離</string>
    <string name="settings_vold_app_data_isolation">Vold app data 隔離</string>
    <string name="settings_change_require_root">此更動需要 root 權限</string>
    <string name="settings_need_reboot">重新啟動以確認更改</string>
    <string name="settings_permission_denied">授權遭拒絕</string>
    <string name="settings_force_mount_data">為所有應用程式啟用資料隔離</string>
    <string name="settings_force_mount_data_summary">資料隔離對於 Target API 低於 30 的應用程式預設停用</string>
    <string name="settings_detail_log">除錯用的詳細日誌</string>
    <string name="settings_max_log_size">記錄器的緩衝區大小</string>
    <string name="settings_hide_icon">在啟動器中隱藏圖示</string>
    <string name="settings_hide_icon_summary">你仍然可以在 Xposed 管理器中開啟並管理此應用</string>
    <string name="settings_language">語言</string>
    <string name="settings_translate">參與翻譯</string>
    <string name="settings_translate_summary">幫助我們翻譯 %s 到您的語言</string>
    <string name="settings_theme">主題</string>
    <string name="settings_system_theme_color">系統主題色</string>
    <string name="settings_theme_color">主題色</string>
    <string name="settings_dark_theme">深色主題</string>
    <string name="settings_pure_black_dark_theme">純黑的深色主題</string>
    <string name="settings_pure_black_dark_theme_summary">如果啟用了深色主題，則使用純黑色的背景色</string>
    <string name="settings_service">服務</string>
    <string name="settings_stop_system_service">停止系統服務</string>
    <string name="settings_stop_system_service_summary">需要重啟來重啟服務</string>
    <string name="settings_is_clean_env">同時清理執行環境?</string>
    <string name="settings_is_clean_env_summary">刪除 /data/misc/hide_my_applist_* 中的系統服務快取。這意味著你需要在下次重啟後手動啟動一次模組應用，以便啟用攔截，攔截次數也將被清理</string>
    <string name="settings_force_clean_env">強制清理執行環境 (root)</string>
    <string name="settings_force_clean_env_summary">只有在系統服務無法啟動的情況下使用</string>
    <string name="settings_force_clean_env_toast_successful">執行環境已清理</string>
    <string name="settings_update">更新</string>
    <string name="settings_disable_update">禁用更新通知</string>
    <string name="settings_receive_beta_update">接收 Beta 版更新</string>
    <string name="settings_receive_beta_update_summary">注意：Beta 版可能會相當不穩定</string>
    <!-- About -->
    <string name="about_description">        雖然檢測特定的應用程式安裝來判斷使否有使用 root 是不正卻也不應該的，但並不是每個使用 root 的應用程式都提供隨機包名的功能。\n在這種情況下，檢測到的使用 root 的應用程式 (例如 Fake Location 和儲存隔離) 就等於檢測到了 root 本身。\n\n同時，一些應用會利用各種漏洞來獲取你的應用列表，並預設你是使用 root 的使用者。\n\n這個模組提供了一些方法來測試你是否已經很好地隱藏了你的應用列表。\n同時它可以作為一個 Xposed 模塊來隱藏一些應用程式或拒絕應用程式列表請求，以保護你的隱私。
    </string>
    <string name="about_how_to_use_title">如何使用此模組</string>
    <string name="about_how_to_use_description_1">        首先在 「模板管理」中建立模板。\n然後在 「應用管理」選擇想隱藏應用列表的應用程式並「啟用隱藏」並選擇設定好的模板。\n(當然你也可以選擇額外的應用程式隱藏或顯示)\n注意：你應該而且只應該在 Xposed 模組作用域或白名單中僅勾選「系統框架」(某些系統中可能會將系統框架顯示為 Android)
    </string>
    <string name="about_how_to_use_description_2">        修改設定會立刻生效
    </string>
    <string name="about_developer">開發者</string>
    <string name="about_support">支援和反饋</string>
    <string name="about_open_source">開源許可證</string>
    <!-- Colors -->
    <string name="color_sakura">櫻花</string>
    <string name="color_red">紅色</string>
    <string name="color_pink">粉紅色</string>
    <string name="color_purple">紫色</string>
    <string name="color_deep_purple">深紫色</string>
    <string name="color_indigo">靛藍</string>
    <string name="color_blue">藍色</string>
    <string name="color_light_blue">淺藍色</string>
    <string name="color_cyan">青色</string>
    <string name="color_teal">藍綠色</string>
    <string name="color_green">綠色</string>
    <string name="color_light_green">淺綠色</string>
    <string name="color_lime">青檸色</string>
    <string name="color_yellow">黃色</string>
    <string name="color_amber">琥珀色</string>
    <string name="color_orange">橘色</string>
    <string name="color_deep_orange">深橘色</string>
    <string name="color_brown">棕色</string>
    <string name="color_blue_grey">藍灰色</string>
</resources>
