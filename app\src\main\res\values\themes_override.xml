<?xml version="1.0" encoding="utf-8"?><!--
  ~ This file is part of LSPosed.
  ~
  ~ LSPosed is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by
  ~ the Free Software Foundation, either version 3 of the License, or
  ~ (at your option) any later version.
  ~
  ~ LSPosed is distributed in the hope that it will be useful,
  ~ but WITHOUT ANY WARRANTY; without even the implied warranty of
  ~ MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  ~ GNU General Public License for more details.
  ~
  ~ You should have received a copy of the GNU General Public License
  ~ along with LSPosed.  If not, see <https://www.gnu.org/licenses/>.
  ~
  ~ Copyright (C) 2020 EdXposed Contributors
  ~ Copyright (C) 2021 LSPosed Contributors
  -->

<resources xmlns:tools="http://schemas.android.com/tools" tools:ignore="PrivateResource,UnusedResources">

    <!-- Add android:colorControlNormal -->
    <style name="Base.ThemeOverlay.AppCompat.ActionBar" tools:override="true">
        <item name="android:colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>

    <style name="Base.ThemeOverlay.AppCompat.Dark.ActionBar" tools:override="true">
        <item name="android:colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="colorControlNormal">?android:attr/textColorPrimary</item>
        <item name="searchViewStyle">@style/Widget.AppCompat.SearchView.ActionBar</item>
    </style>

    <style name="Widget.MaterialComponents.CompoundButton.CheckBox" parent="Widget.AppCompat.CompoundButton.CheckBox" tools:override="true">
        <item name="enforceMaterialTheme">true</item>
        <item name="useMaterialThemeColors">true</item>
    </style>

    <style name="Widget.MaterialComponents.CompoundButton.RadioButton" parent="Widget.AppCompat.CompoundButton.RadioButton" tools:override="true">
        <item name="enforceMaterialTheme">true</item>
        <item name="useMaterialThemeColors">true</item>
    </style>

    <style name="ThemeOverlay.Material.Dialog.ColorPicker" parent="@style/ThemeOverlay.Material3.MaterialAlertDialog.Centered" tools:override="true" />

    <!-- Base themes -->
    <style name="Base.Light" parent="Theme.Material3.Light" tools:override="true">
        <item name="android:statusBarColor">?android:colorBackground</item>
        <item name="android:ambientShadowAlpha">@dimen/ambient_shadow_alpha</item>
        <item name="android:spotShadowAlpha">@dimen/spot_shadow_alpha</item>

        <item name="colorPrimary">@color/primary_light</item>
        <item name="colorPrimaryVariant">@color/primary_dark_light</item>
        <item name="swipeRefreshLayoutProgressSpinnerBackgroundColor">?cardBackground</item>

        <item name="colorAlert">@color/material_red_A200</item>
        <item name="colorSafe">@color/material_green_600</item>
        <item name="colorWarning">@color/material_orange_700</item>
        <item name="colorInactive" tools:ignore="PrivateResource">@color/material_grey_600</item>

        <item name="navigationBarDividerColor">@android:color/transparent</item>
    </style>

    <style name="Base" parent="Theme.Material3.Dark" tools:override="true">
        <item name="android:statusBarColor">?android:colorBackground</item>
        <item name="android:ambientShadowAlpha">@dimen/ambient_shadow_alpha</item>
        <item name="android:spotShadowAlpha">@dimen/spot_shadow_alpha</item>

        <item name="colorPrimary">@color/primary_dark</item>
        <item name="colorPrimaryVariant">@color/primary_dark_dark</item>
        <item name="swipeRefreshLayoutProgressSpinnerBackgroundColor">?cardBackground</item>

        <item name="colorAlert">@color/material_red_A100</item>
        <item name="colorSafe">@color/material_green_A100</item>
        <item name="colorWarning">@color/material_orange_A100</item>

        <item name="navigationBarDividerColor">@android:color/transparent</item>
    </style>
</resources>
