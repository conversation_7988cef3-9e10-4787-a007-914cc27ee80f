<!--
  ~ This file is part of LSPosed.
  ~
  ~ LSPosed is free software: you can redistribute it and/or modify
  ~ it under the terms of the GNU General Public License as published by
  ~ the Free Software Foundation, either version 3 of the License, or
  ~ (at your option) any later version.
  ~
  ~ LSPosed is distributed in the hope that it will be useful,
  ~ but WITHOUT ANY WARRANTY; without even the implied warranty of
  ~ MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
  ~ GNU General Public License for more details.
  ~
  ~ You should have received a copy of the GNU General Public License
  ~ along with LSPosed.  If not, see <https://www.gnu.org/licenses/>.
  ~
  ~ Copyright (C) 2020 EdXposed Contributors
  ~ Copyright (C) 2021 LSPosed Contributors
  -->

<resources>

    <style name="AppTheme" parent="Theme.Light" />

    <!-- SubtitleCollapsingToolbarLayout styles -->
    <style name="Widget.Design.SubtitleCollapsingToolbar" parent="Widget.Design.CollapsingToolbar" />

    <style name="TextAppearance.Design.SubtitleCollapsingToolbar.ExpandedTitle" parent="TextAppearance.Design.CollapsingToolbar.Expanded" />

    <style name="TextAppearance.Design.SubtitleCollapsingToolbar.ExpandedSubtitle" parent="TextAppearance.AppCompat.Title">
        <item name="android:textColor">?android:attr/textColorSecondary</item>
    </style>

    <style name="language_menu_style" parent="ThemeOverlay.Preference.SimpleMenuPreference.PopupMenu">
        <item name="android:textAlignment">textStart</item>
    </style>
    <style name="Widget.Button.TextButton.Dialog.FullWidth" parent="Widget.Material3.Button.TextButton.Dialog.Flush">
        <item name="android:layout_width">0dp</item>
        <item name="android:layout_weight">1</item>
        <item name="android:maxWidth">@null</item>
    </style>
    <style name="ThemeOverlay.MaterialAlertDialog.Centered.FullWidthButtons" parent="ThemeOverlay.Material3.MaterialAlertDialog.Centered">
        <!-- Mark spacer as gone when showing full width buttons -->
        <item name="materialAlertDialogButtonSpacerVisibility">2</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.Button.TextButton.Dialog.FullWidth</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.Button.TextButton.Dialog.FullWidth</item>
        <item name="buttonBarNeutralButtonStyle">@style/Widget.Button.TextButton.Dialog.FullWidth</item>
    </style>
    <style name="ThemeOverlay.MaterialAlertDialog.FullWidthButtons" parent="ThemeOverlay.Material3.MaterialAlertDialog">
        <!-- Mark spacer as gone when showing full width buttons -->
        <item name="materialAlertDialogButtonSpacerVisibility">2</item>
        <item name="buttonBarPositiveButtonStyle">@style/Widget.Button.TextButton.Dialog.FullWidth</item>
        <item name="buttonBarNegativeButtonStyle">@style/Widget.Button.TextButton.Dialog.FullWidth</item>
        <item name="buttonBarNeutralButtonStyle">@style/Widget.Button.TextButton.Dialog.FullWidth</item>
    </style>
</resources>
