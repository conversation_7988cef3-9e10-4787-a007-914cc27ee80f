<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="app_name">隐藏应用列表</string>
    <string name="title_home">首页</string>
    <string name="title_logs">日志</string>
    <string name="title_settings">设置</string>
    <string name="title_template_manage">模板管理</string>
    <string name="title_template_settings">模板设置</string>
    <string name="title_app_manage">应用管理</string>
    <string name="title_app_select">选择应用</string>
    <string name="title_app_settings">应用配置</string>
    <string name="title_about">关于</string>
    <string name="do_not_dual">请勿多开或将隐藏应用列表安装到其他用户</string>
    <string name="show_crash_log">显示崩溃日志</string>
    <string name="config_damaged">配置文件损坏，请清除应用数据</string>
    <string name="edit_list">编辑列表</string>
    <string name="delete">删除</string>
    <string name="enabled">启用</string>
    <string name="disabled">已禁用</string>
    <string name="work_mode">工作模式</string>
    <string name="blacklist">黑名单</string>
    <string name="whitelist">白名单</string>
    <string name="application">应用</string>
    <string name="preferences">偏好设置</string>
    <string name="refresh">刷新</string>
    <string name="filter">筛选</string>
    <string name="yes">是</string>
    <string name="no">否</string>
    <string name="search">搜索</string>
    <string name="system">系统</string>
    <!-- Home -->
    <string name="home_xposed_activated">模块已激活 [%s-%d]</string>
    <string name="home_xposed_not_activated">模块未激活</string>
    <string name="home_xposed_service_on">系统服务运行中 [%d]</string>
    <string name="home_xposed_service_off">系统服务未运行</string>
    <string name="home_xposed_service_old">模块已更新，需要重启设备</string>
    <string name="home_xposed_filter_count">隐藏应用列表已经执行了 %d 次拦截</string>
    <string name="home_manage">管理</string>
    <string name="home_detection_test">拦截测试</string>
    <string name="home_download_test_app_title">下载拦截测试应用</string>
    <string name="home_download_test_app_message">我们开发了一个独立的拦截测试应用，是否现在下载？</string>
    <string name="home_backup_and_restore">备份 &amp; 还原</string>
    <string name="home_backup_config">备份配置</string>
    <string name="home_restore_config">还原配置</string>
    <string name="home_import_successful">导入成功</string>
    <string name="home_import_failed">导入失败</string>
    <string name="home_import_app_version_too_old">模块版本低于备份版本</string>
    <string name="home_import_backup_version_too_old">备份版本过低</string>
    <string name="home_import_file_damaged">备份文件损坏</string>
    <string name="home_export_failed">导出失败</string>
    <string name="home_exported">备份完成</string>
    <string name="home_new_update">更新可用： %s</string>
    <string name="home_update">更新： %s</string>
    <!-- App select -->
    <string name="list_show_system">显示系统应用</string>
    <string name="list_sorting">排序</string>
    <string name="list_reverse_order">倒序</string>
    <string name="list_sort_by_label">按应用名称</string>
    <string name="list_sort_by_package_name">按应用包名</string>
    <string name="list_sort_by_install_time">按安装时间</string>
    <string name="list_sort_by_update_time">按更新时间</string>
    <!-- Template manage -->
    <string name="template_usage_hint">为“不安分”的应用建立模板，控制可获取的应用列表。\n\n黑名单（推荐）：模板中选中的应用对应用模板的应用不可见。\n白名单：模板中未选中的应用对应用模板的应用不可见。</string>
    <string name="template_new_blacklist">创建黑名单模板</string>
    <string name="template_new_whitelist">创建白名单模板</string>
    <string name="template_delete">你确定要删除此模板吗？</string>
    <string name="template_name">模板名称</string>
    <string name="template_name_invalid">名称无效</string>
    <string name="template_delete_title">删除模板</string>
    <string name="template_name_already_exist">模板名称已存在</string>
    <string name="template_applied_count">已应用于 %d 个应用</string>
    <string name="template_apps_visible_count">%d 个应用可见</string>
    <string name="template_apps_invisible_count">%d 个应用不可见</string>
    <!-- App settings -->
    <string name="app_enable_hide">启用隐藏</string>
    <string name="app_template_config">模板设置</string>
    <string name="app_choose_template">选择模板</string>
    <string name="app_exclude_system_apps">排除系统应用</string>
    <string name="app_exclude_system_apps_summary">仅在必需时关闭此选项。禁用它很可能会导致崩溃</string>
    <string name="app_template_using">已启用 %d 个模板</string>
    <string name="app_extra_apps_visible_count">%d 个额外可见的应用</string>
    <string name="app_extra_apps_invisible_count">%d 个额外不可见的应用</string>
    <!-- Logs -->
    <string name="logs_save">保存日志</string>
    <string name="logs_clear">清除日志</string>
    <string name="logs_empty">没有可保存的日志</string>
    <string name="logs_saved">日志已保存</string>
    <!-- Settings -->
    <string name="settings_module">模块</string>
    <string name="settings_data_isolation">数据隔离</string>
    <string name="settings_data_isolation_summary">App data：%s\nVold app data：%s\n强制挂载：%s</string>
    <string name="settings_data_isolation_unsupported">此选项仅适用于 Android 11 及以上版本的系统</string>
    <string name="settings_app_data_isolation">App data 隔离</string>
    <string name="settings_vold_app_data_isolation">Vold app data 隔离</string>
    <string name="settings_warning">警告</string>
    <string name="settings_vold_warning">这将启用 AOSP 中用于隔离应用外部私有存储目录的功能。这可以帮助改善隐藏效果，但可能会导致稳定性问题。此功能在 AOSP 中已被废弃，可能随时停止工作。</string>
    <string name="settings_change_require_root">此更改需要 root 权限</string>
    <string name="settings_need_reboot">重启以应用更改</string>
    <string name="settings_permission_denied">权限被拒绝</string>
    <string name="settings_force_mount_data">为所有应用启用数据隔离</string>
    <string name="settings_force_mount_data_summary">数据隔离对于 Target API 低于 30 的应用默认禁用</string>
    <string name="settings_detail_log">详细日志</string>
    <string name="settings_max_log_size">日志缓冲区大小</string>
    <string name="settings_hide_icon">在启动器中隐藏图标</string>
    <string name="settings_hide_icon_summary">你仍然可以在 Xposed 管理器中打开此应用</string>
    <string name="settings_language">语言</string>
    <string name="settings_translate">参与翻译</string>
    <string name="settings_translate_summary">帮助我们把 %s 翻译到你的语言</string>
    <string name="settings_theme">主题</string>
    <string name="settings_system_theme_color">系统主题色</string>
    <string name="settings_theme_color">主题颜色</string>
    <string name="settings_dark_theme">深色主题</string>
    <string name="settings_pure_black_dark_theme">纯黑主题</string>
    <string name="settings_pure_black_dark_theme_summary">当深色主题启用时使用纯黑主题</string>
    <string name="settings_service">服务</string>
    <string name="settings_stop_system_service">停止系统服务</string>
    <string name="settings_stop_system_service_summary">需要重启设备才能再次启动系统服务</string>
    <string name="settings_is_clean_env">同时清理运行环境？</string>
    <string name="settings_is_clean_env_summary">删除 /data/misc/hide_my_applist_* 中的系统服务缓存。这意味着你需要在下次重启后手动启动一次模块应用，以便启用拦截，同时拦截次数也将被清理。</string>
    <string name="settings_force_clean_env">强制清理运行环境（root）</string>
    <string name="settings_force_clean_env_summary">只有在系统服务无法启动的情况下才应使用，否则请使用上面的选项。</string>
    <string name="settings_force_clean_env_toast_successful">运行环境已清理</string>
    <string name="settings_update">更新</string>
    <string name="settings_disable_update">禁用更新通知</string>
    <string name="settings_receive_beta_update">接收测试版更新</string>
    <string name="settings_receive_beta_update_summary">注意：测试版可能不稳定</string>
    <!-- About -->
    <string name="about_description">虽然“检测安装的应用”是不正确的做法，但是并不是所有的与 root 相关联的插件类应用都提供了随机包名支持。\n这就意味着检测到安装了此类应用（如 Fake Location 、存储空间隔离）与检测到了 root 本身区别不大。\n\n与此同时，部分“不安分”的应用会使用各种漏洞绕过系统权限来获取你的应用列表，从而对你建立用户画像。\n\n该模块提供了一些检测方式用于测试您是否成功地隐藏了某些特定的包名；同时可作为 Xposed 模块用于隐藏应用列表或特定应用，保护隐私。    </string>
    <string name="about_how_to_use_title">如何使用此模块</string>
    <string name="about_how_to_use_description_1">您可以在“模板管理”中创建模板。\n然后，将模板应用于“应用管理”中的应用。\n（您也可以为目标应用选择额外可见或不可见的应用。）\n注意：您应确保在 Xposed 中设置的模块作用域/白名单中有且仅有“系统框架”。(部分系统中“系统框架”可能显示为“Android”)    </string>
    <string name="about_how_to_use_description_2">配置修改即时生效。    </string>
    <string name="about_developer">开发者</string>
    <string name="about_support">支持与反馈</string>
    <string name="about_open_source">开放源代码许可</string>
    <!-- Colors -->
    <string name="color_sakura">樱花</string>
    <string name="color_red">红色</string>
    <string name="color_pink">粉色</string>
    <string name="color_purple">紫色</string>
    <string name="color_deep_purple">深紫</string>
    <string name="color_indigo">靛青</string>
    <string name="color_blue">蓝色</string>
    <string name="color_light_blue">浅蓝</string>
    <string name="color_cyan">青色</string>
    <string name="color_teal">青绿</string>
    <string name="color_green">绿色</string>
    <string name="color_light_green">浅绿</string>
    <string name="color_lime">黄绿</string>
    <string name="color_yellow">黄色</string>
    <string name="color_amber">琥珀</string>
    <string name="color_orange">橙色</string>
    <string name="color_deep_orange">深橙</string>
    <string name="color_brown">棕色</string>
    <string name="color_blue_grey">灰蓝</string>
</resources>
