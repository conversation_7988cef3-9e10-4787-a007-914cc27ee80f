[versions]
agp = "8.9.0"
kotlin = "2.1.10"
rxhttp = "3.3.1"
hidden-api = "4.3.3"

[plugins]
agp-app = { id = "com.android.application", version.ref = "agp" }
agp-lib = { id = "com.android.library", version.ref = "agp" }
kotlin = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-serialization = { id = "org.jetbrains.kotlin.plugin.serialization", version.ref = "kotlin" }
ksp = { id = "com.google.devtools.ksp", version = "2.1.10-1.0.31" }
gms = { id = "com.google.gms.google-services", version = "4.4.2" }
nav-safeargs-kotlin = { id = "androidx.navigation.safeargs.kotlin", version = "2.8.8" }
autoresconfig = { id = "dev.rikka.tools.autoresconfig", version = "1.2.2" }
materialthemebuilder = { id = "dev.rikka.tools.materialthemebuilder", version = "1.5.1" }
refine = { id = "dev.rikka.tools.refine", version = "4.4.0" }

[libraries]
androidx-annotation-jvm = { group = "androidx.annotation", name = "annotation-jvm", version = "1.9.1" }
androidx-navigation-fragment-ktx = { module = "androidx.navigation:navigation-fragment-ktx", version = "2.8.8" }
androidx-navigation-ui-ktx = { module = "androidx.navigation:navigation-ui-ktx", version = "2.8.8" }
androidx-preference-ktx = { module = "androidx.preference:preference-ktx", version = "1.2.1" }
androidx-swiperefreshlayout = { module = "androidx.swiperefreshlayout:swiperefreshlayout", version = "1.1.0" }
com-android-tools-build-apksig = { module = "com.android.tools.build:apksig", version.ref = "agp" }
com-drakeet-about = { module = "com.drakeet.about:about", version = "2.5.2" }
com-drakeet-multitype = { module = "com.drakeet.multitype:multitype", version = "4.3.0" }
com-github-kirich1409-viewbindingpropertydelegate = { module = "com.github.kirich1409:viewbindingpropertydelegate", version = "1.5.9" }
com-github-kyuubiran-ezxhelper = { module = "com.github.kyuubiran:EzXHelper", version = "1.0.3" }
com-github-liujingxing-rxhttp = { module = "com.github.liujingxing.rxhttp:rxhttp", version.ref = "rxhttp" }
com-github-liujingxing-rxhttp-compiler = { module = "com.github.liujingxing.rxhttp:rxhttp-compiler", version.ref = "rxhttp" }
com-github-liujingxing-rxhttp-converter-serialization = { module = "com.github.liujingxing.rxhttp:converter-serialization", version.ref = "rxhttp" }
com-github-topjohnwu-libsu-core = { module = "com.github.topjohnwu.libsu:core", version = "6.0.0" }
com-google-android-material = { module = "com.google.android.material:material", version = "1.12.0" }
com-google-android-gms-play-services-ads = { module = "com.google.android.gms:play-services-ads", version = "24.0.0" }
com-google-firebase-bom = { module = "com.google.firebase:firebase-bom", version = "33.10.0" }
com-google-firebase-analytics-ktx = { module = "com.google.firebase:firebase-analytics-ktx", version = "22.3.0" }
com-squareup-okhttp3 = { module = "com.squareup.okhttp3:okhttp", version = "4.12.0" }
de-robv-android-xposed-api = { module = "de.robv.android.xposed:api", version = "82" }
dev-rikka-hidden-compat = { module = "dev.rikka.hidden:compat", version.ref = "hidden-api" }
dev-rikka-hidden-stub = { module = "dev.rikka.hidden:stub", version.ref = "hidden-api" }
dev-rikka-rikkax-material = { module = "dev.rikka.rikkax.material:material", version = "2.7.2" }
dev-rikka-rikkax-material-preference = { module = "dev.rikka.rikkax.material:material-preference", version = "2.0.0" }
kotlinx-serialization-json = { module = "org.jetbrains.kotlinx:kotlinx-serialization-json", version = "1.7.3" }
me-zhanghai-android-appiconloader = { module = "me.zhanghai.android.appiconloader:appiconloader", version = "1.5.0" }
